import { error } from '@sveltejs/kit';
import { getDatabase } from '$lib/mongodb.js';
import { ObjectId } from 'mongodb';

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  try {
    const computerId = url.searchParams.get('computerId');
    const productDesignation = url.searchParams.get('productDesignation');

    if (!computerId) {
      throw error(400, 'Computer ID is required');
    }

    // Validate ObjectId format
    if (!ObjectId.isValid(computerId)) {
      throw error(400, 'Invalid Computer ID format');
    }

    console.log('Loading Engine Package Modelling data for:', { computerId, productDesignation });

    const db = await getDatabase();

    // Get computer information
    const computer = await db.collection('CustomerComputers').findOne({
      _id: new ObjectId(computerId)
    });

    if (!computer) {
      throw error(404, 'Computer not found');
    }

    // Get customer information
    const customerId = computer.customerId instanceof ObjectId
      ? computer.customerId
      : new ObjectId(computer.customerId);

    const customer = await db.collection('Customers').findOne({
      _id: customerId
    });

    if (!customer) {
      throw error(404, 'Customer not found');
    }

    // Extract computer category from model (e.g., "D16-B-GEN-10" -> "D16")
    const computerModel = computer.model || '';
    const computerCategory = computerModel.match(/^([A-Z]+\d+)/)?.[1] || '';

    console.log('Computer details:', {
      model: computerModel,
      category: computerCategory,
      productDesignation: productDesignation
    });

    // Helper function to serialize MongoDB documents
    const serializeDocument = (doc) => {
      if (!doc) return null;
      const result = { ...doc };
      // Convert ObjectId to string
      if (result._id instanceof ObjectId) {
        result._id = result._id.toString();
      }
      // Convert any other ObjectIds to strings
      Object.keys(result).forEach(key => {
        if (result[key] instanceof ObjectId) {
          result[key] = result[key].toString();
        }
      });
      return result;
    };

    const serializeDocuments = (docs) => {
      if (!docs || !Array.isArray(docs)) return [];
      return docs.map(serializeDocument);
    };

    // Get Product Designation data first to determine ProductValidityGroup
    const productDesignationData = await db.collection('ProductDesignation').find({
      ProductDesignation: productDesignation
    }).toArray();
    console.log(`Found ${productDesignationData.length} Product Designation records for ${productDesignation}`);

    // Get ProductValidityGroup from ProductDesignation or use productDesignation as fallback
    let productValidityGroup = productDesignation;
    if (productDesignationData.length > 0) {
      productValidityGroup = productDesignationData[0].ProductValidityGroup || productDesignation;
    }
    console.log(`Using ProductValidityGroup: ${productValidityGroup}`);

    // Get product-specific Engine Package Modelling data
    const enginePackageData = await db.collection('EnginePackageModelling').find({
      $or: [
        { ProductDesignation: productDesignation },
        { ProductValidityGroup: productValidityGroup }
      ]
    }).toArray();
    console.log(`Found ${enginePackageData.length} Engine Package Modelling records for ${productDesignation}`);

    // Get product-specific Engine Variants data
    const engineVariants = await db.collection('EngineVariants').find({
      $or: [
        { ProductDesignation: productDesignation },
        { ProductValidityGroup: productValidityGroup }
      ]
    }).toArray();
    console.log(`Found ${engineVariants.length} Engine Variants records for ${productDesignation}`);

    // Get product-specific Service data for service levels
    const serviceCodeData = await db.collection('ServiceCodeAndActionType').find({
      ProductValidityGroup: productValidityGroup
    }).toArray();
    console.log(`Found ${serviceCodeData.length} Service Code records for ${productValidityGroup}`);

    // Get product-specific Base Services data
    const baseServices = await db.collection('BaseServices').find({
      $or: [
        { ProductValidityGroup: productValidityGroup },
        { universalOffer: true }
      ]
    }).toArray();
    console.log(`Found ${baseServices.length} Base Services records for ${productValidityGroup}`);

    // Get pricing data from PriceList for this product
    const priceListData = await db.collection('PriceList').find({
      $or: [
        { "Product group": productValidityGroup },
        { Description: { $regex: productDesignation, $options: 'i' } }
      ]
    }).toArray();
    console.log(`Found ${priceListData.length} Price List records for ${productDesignation}`);

    // Get Business Models data
    const businessModels = await db.collection('BusinessModels').find({}).toArray();
    console.log(`Found ${businessModels.length} Business Models records`);

    // Get Business Functions data
    const businessFunctions = await db.collection('BusinessFunctions').find({}).toArray();
    console.log(`Found ${businessFunctions.length} Business Functions records`);

    // Get Calculations data
    const calculations = await db.collection('Calculations').find({}).toArray();
    console.log(`Found ${calculations.length} Calculations records`);

    return {
      computer: serializeDocument(computer),
      customer: serializeDocument(customer),
      computerCategory,
      productDesignation: productDesignation || '',
      productValidityGroup,
      enginePackageData: serializeDocuments(enginePackageData),
      engineVariants: serializeDocuments(engineVariants),
      serviceCodeData: serializeDocuments(serviceCodeData),
      baseServices: serializeDocuments(baseServices),
      priceListData: serializeDocuments(priceListData),
      businessModels: serializeDocuments(businessModels),
      businessFunctions: serializeDocuments(businessFunctions),
      calculations: serializeDocuments(calculations),
      productDesignationData: serializeDocuments(productDesignationData),
      dataSource: {
        enginePackageModelling: enginePackageData.length,
        engineVariants: engineVariants.length,
        serviceCodeData: serviceCodeData.length,
        baseServices: baseServices.length,
        priceListData: priceListData.length,
        businessModels: businessModels.length,
        businessFunctions: businessFunctions.length,
        calculations: calculations.length,
        productDesignation: productDesignationData.length
      }
    };
  } catch (err) {
    console.error('Error loading Engine Package Modelling data:', err);
    throw error(500, 'Failed to load Engine Package Modelling data');
  }
}
