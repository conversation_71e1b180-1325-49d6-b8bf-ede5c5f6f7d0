<script>
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';

  /** @type {import('./$types').PageData} */
  export let data;

  // Extract data from server
  const {
    computer,
    customer,
    computerCategory,
    productDesignation,
    enginePackageData,
    engineVariants,
    enginePackages,
    businessModels,
    businessFunctions,
    calculations,
    productDesignationData,
    serviceTypes,
    packages,
    dataSource
  } = data;

  // Get computerId from URL for navigation
  const computerId = $page.url.searchParams.get('computerId') || '';

  // Sample data structure based on the image layout
  let quotationList = [
    { id: 1, name: 'Sample Quotation 1', date: '2024-01-15' },
    { id: 2, name: 'Sample Quotation 2', date: '2024-01-20' }
  ];

  // Contract data
  let contractData = {
    standardContractLength: 8760,
    extendedContractLength: 8760,
    contractAge: 1564.4468,
    engineAge: 1564.4468,
    engineOperatingHours: 1.00,
    multipleData: 1.00
  };

  // Engine specific data
  let engineSpecific = {
    customerSpecific: '#VALUE!',
    contractSpecific: '#VALUE!',
    totalFixedPrice: '#VALUE!',
    totalVariablePrice: '#VALUE!'
  };

  // Service data
  let serviceData = [
    { level: 'A', service: 'Base Contract Offering', available: true, price: '#VALUE!', customerSpecific: '#VALUE!', contractSpecific: '#VALUE!' },
    { level: 'B', service: 'Dealer Add-Ons', available: true, price: '#VALUE!', customerSpecific: '#VALUE!', contractSpecific: '#VALUE!' },
    { level: 'C', service: 'Support (Self Service)', available: true, price: '#VALUE!', customerSpecific: '#VALUE!', contractSpecific: '#VALUE!' },
    { level: 'D', service: 'Retirement Plan', available: true, price: '#VALUE!', customerSpecific: '#VALUE!', contractSpecific: '#VALUE!' }
  ];

  // Functions to handle actions
  function addToQuotationList() {
    console.log('Add to Quotation List clicked');
  }

  function clearQuotationList() {
    quotationList = [];
  }

  function clearInputs() {
    console.log('Clear Inputs clicked');
  }

  function clearDetailed() {
    console.log('Clear Detailed Inputs Only clicked');
  }

  function goBack() {
    const url = `/subliststd1-one-page/workload-display?computerId=${computerId}&productDesignation=${productDesignation}`;
    goto(url);
  }
</script>

<svelte:head>
  <title>Engine Package Modelling - {productDesignation}</title>
</svelte:head>

<div class="engine-package-container">
  <!-- Header -->
  <div class="header">
    <button class="back-button" on:click={goBack}>
      ← Back to Workload Display
    </button>
    <h1>Engine Package Modelling</h1>
  </div>

  <!-- Customer Base Information -->
  <div class="info-section">
    <div class="customer-base-info">
      <h3>Customer Base Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Customer Name:</label>
          <span>{customer?.customerName || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Customer ID:</label>
          <span>{customer?._id || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Customer Type:</label>
          <span>{customer?.customerType || 'Fleet Owner'}</span>
        </div>
        <div class="info-item">
          <label>Region:</label>
          <span>{customer?.region || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Country:</label>
          <span>{customer?.country || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Contact:</label>
          <span>{customer?.contactPerson || 'N/A'}</span>
        </div>
      </div>
    </div>

    <div class="engine-base-info">
      <h3>Engine Base Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Product Designation:</label>
          <span>{productDesignation || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Computer ID:</label>
          <span>{computer?._id || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Computer Model:</label>
          <span>{computer?.model || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Computer Category:</label>
          <span>{computerCategory || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Serial Number:</label>
          <span>{computer?.serialNumber || 'N/A'}</span>
        </div>
        <div class="info-item">
          <label>Installation Date:</label>
          <span>{computer?.installationDate ? new Date(computer.installationDate).toLocaleDateString() : 'N/A'}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Section with Customer Type and CRM -->
  <div class="top-section">
    <div class="customer-type-section">
      <label>Customer Type*</label>
      <input type="text" value={customer?.customerType || "Fleet Owner"} readonly />
    </div>
    <div class="crm-section">
      <label>CRM (Required)</label>
      <input type="text" value="" />
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="main-grid">
    <!-- Left Column -->
    <div class="left-column">
      <!-- Contract Data Section -->
      <div class="contract-data-section">
        <h3>Contract Data</h3>
        <div class="data-grid">
          <div class="data-row">
            <label>Standard Contract Length (hrs)*</label>
            <input type="number" bind:value={contractData.standardContractLength} />
          </div>
          <div class="data-row">
            <label>Extended Contract Length (hrs)*</label>
            <input type="number" bind:value={contractData.extendedContractLength} />
          </div>
          <div class="data-row">
            <label>Contract Age (hrs)*</label>
            <input type="number" bind:value={contractData.contractAge} />
          </div>
          <div class="data-row">
            <label>Engine Age (hrs)*</label>
            <input type="number" bind:value={contractData.engineAge} />
          </div>
          <div class="data-row">
            <label>Engine Operating Hours*</label>
            <input type="number" step="0.01" bind:value={contractData.engineOperatingHours} />
          </div>
          <div class="data-row">
            <label>Multiple Data*</label>
            <input type="number" step="0.01" bind:value={contractData.multipleData} />
          </div>
        </div>
      </div>

      <!-- Service Levels Section -->
      <div class="service-levels-section">
        <h3>Service Levels</h3>
        <div class="service-table">
          <div class="service-header">
            <div>Level</div>
            <div>Service</div>
            <div>Available</div>
            <div>Price</div>
            <div>Customer Specific</div>
            <div>Contract Specific</div>
          </div>
          {#each serviceData as service}
            <div class="service-row">
              <div class="level-cell">{service.level}</div>
              <div class="service-cell">{service.service}</div>
              <div class="available-cell">
                <input type="checkbox" bind:checked={service.available} />
              </div>
              <div class="price-cell">{service.price}</div>
              <div class="customer-cell">{service.customerSpecific}</div>
              <div class="contract-cell">{service.contractSpecific}</div>
            </div>
          {/each}
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="right-column">
      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="action-btn add-btn" on:click={addToQuotationList}>
          Add Deliveries Quote to Quotation List
        </button>
        <button class="action-btn clear-btn" on:click={clearQuotationList}>
          Clear Quotation List
        </button>
        <button class="action-btn clear-btn" on:click={clearInputs}>
          Clear Inputs
        </button>
        <button class="action-btn clear-detailed-btn" on:click={clearDetailed}>
          Clear Detailed Inputs Only
        </button>
      </div>

      <!-- Engine Specific Section -->
      <div class="engine-specific-section">
        <h3>Engine Specific</h3>
        <div class="engine-data">
          <div class="engine-row">
            <label>Customer Specific</label>
            <span>{engineSpecific.customerSpecific}</span>
          </div>
          <div class="engine-row">
            <label>Contract Specific</label>
            <span>{engineSpecific.contractSpecific}</span>
          </div>
          <div class="engine-row">
            <label>Total Fixed Price</label>
            <span>{engineSpecific.totalFixedPrice}</span>
          </div>
          <div class="engine-row">
            <label>Total Variable Price</label>
            <span>{engineSpecific.totalVariablePrice}</span>
          </div>
        </div>
      </div>

      <!-- Quotation List -->
      <div class="quotation-list-section">
        <h3>Quotation List</h3>
        {#if quotationList.length > 0}
          <div class="quotation-items">
            {#each quotationList as item}
              <div class="quotation-item">
                <span>{item.name}</span>
                <span>{item.date}</span>
              </div>
            {/each}
          </div>
        {:else}
          <div class="no-quotations">No quotations in list</div>
        {/if}
      </div>
    </div>
  </div>

  <!-- Debug Information -->
  <div class="debug-section">
    <h3>Debug Information</h3>
    <p><strong>Product Designation:</strong> {productDesignation}</p>
    <p><strong>Computer Category:</strong> {computerCategory}</p>
    <p><strong>Data Sources:</strong></p>
    <ul>
      <li>Engine Package Modelling: {dataSource.enginePackageModelling} records</li>
      <li>Engine Variants: {dataSource.engineVariants} records</li>
      <li>Engine Packages: {dataSource.enginePackages} records</li>
      <li>Business Models: {dataSource.businessModels} records</li>
      <li>Business Functions: {dataSource.businessFunctions} records</li>
      <li>Calculations: {dataSource.calculations} records</li>
    </ul>
  </div>
</div>

<style>
  .engine-package-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    font-family: Arial, sans-serif;
  }

  .header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .back-button {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.5rem 0;
  }

  .back-button:hover {
    text-decoration: underline;
  }

  h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
  }

  /* Information Section Styles */
  .info-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .customer-base-info,
  .engine-base-info {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .customer-base-info h3,
  .engine-base-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    border-bottom: 2px solid #3b82f6;
    padding-bottom: 0.5rem;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-item label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  .info-item span {
    font-size: 0.875rem;
    color: #1e293b;
    background-color: #f8fafc;
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #e2e8f0;
    word-break: break-all;
  }

  .top-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .customer-type-section,
  .crm-section {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .customer-type-section label,
  .crm-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
  }

  .customer-type-section input,
  .crm-section input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .left-column,
  .right-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .contract-data-section,
  .service-levels-section,
  .engine-specific-section,
  .quotation-list-section {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .contract-data-section h3,
  .service-levels-section h3,
  .engine-specific-section h3,
  .quotation-list-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
  }

  .data-grid {
    display: grid;
    gap: 0.75rem;
  }

  .data-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.5rem;
    align-items: center;
  }

  .data-row label {
    font-size: 0.875rem;
    color: #374151;
  }

  .data-row input {
    padding: 0.375rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .service-table {
    display: grid;
    grid-template-columns: 60px 2fr 80px 100px 120px 120px;
    gap: 1px;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
  }

  .service-header {
    display: contents;
  }

  .service-header > div {
    background-color: #f3f4f6;
    padding: 0.5rem 0.25rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-align: center;
  }

  .service-row {
    display: contents;
  }

  .service-row > div {
    background-color: white;
    padding: 0.5rem 0.25rem;
    font-size: 0.875rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .level-cell {
    font-weight: 600;
  }

  .service-cell {
    text-align: left !important;
    justify-content: flex-start !important;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.875rem;
  }

  .add-btn {
    background-color: #10b981;
    color: white;
  }

  .add-btn:hover {
    background-color: #059669;
  }

  .clear-btn {
    background-color: #ef4444;
    color: white;
  }

  .clear-btn:hover {
    background-color: #dc2626;
  }

  .clear-detailed-btn {
    background-color: #f59e0b;
    color: white;
  }

  .clear-detailed-btn:hover {
    background-color: #d97706;
  }

  .engine-data {
    display: grid;
    gap: 0.5rem;
  }

  .engine-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.5rem;
    align-items: center;
    padding: 0.375rem;
    background-color: #f8fafc;
    border-radius: 0.25rem;
  }

  .engine-row label {
    font-size: 0.875rem;
    color: #374151;
  }

  .engine-row span {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e293b;
  }

  .quotation-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .quotation-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: #f8fafc;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .no-quotations {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 1rem;
  }

  .debug-section {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #ddd;
  }

  .debug-section h3 {
    color: #333;
    margin-bottom: 1rem;
  }

  .debug-section p,
  .debug-section ul {
    margin: 0.5rem 0;
    color: #666;
  }

  .debug-section ul {
    padding-left: 1.5rem;
  }

  @media (max-width: 1024px) {
    .info-section {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .main-grid {
      grid-template-columns: 1fr;
    }

    .top-section {
      grid-template-columns: 1fr;
    }

    .service-table {
      grid-template-columns: 50px 1fr 60px 80px 100px 100px;
      font-size: 0.75rem;
    }
  }

  @media (max-width: 768px) {
    .engine-package-container {
      padding: 0.5rem;
    }

    .customer-base-info,
    .engine-base-info {
      padding: 1rem;
    }

    .info-item span {
      word-break: break-word;
    }
  }
</style>
